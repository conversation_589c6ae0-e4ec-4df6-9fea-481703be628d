"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { SoundButton } from "@/app/components/ui/sound-button";
import { Input } from "@/app/components/ui/input";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { LuPackage, LuPower, LuInfo, LuZap, LuFilter, LuSearch, LuArrowRight, LuClock } from "react-icons/lu";
import { Skeleton } from "@/app/components/ui/skeleton";

// Fungsi untuk mendapatkan kapasitas dari string
function extractCapacity(capacity: number | string): string {
  if (typeof capacity === 'number') {
    return `${capacity} kVA`;
  }
  return String(capacity);
}

// Fungsi untuk mendapatkan tipe genset
function getGensetType(category: string | null): string {
  if (!category) return 'Standard';
  return category;
}

// Fungsi untuk estimasi konsumsi BBM
function estimateFuelConsumption(capacity: number | string): string {
  const numCapacity = typeof capacity === 'number' ? capacity : parseFloat(String(capacity));
  if (isNaN(numCapacity)) return '0 L/jam';

  // Estimasi kasar: 0.2L/jam per kVA
  const consumption = (numCapacity * 0.2).toFixed(1);
  return `${consumption} L/jam`;
}

// Tipe untuk produk
interface Product {
  id: string;
  name: string;
  price: number;
  capacity: number | string;
  category: string | null;
  status: string;
  imageUrl?: string;
  image?: string;
  overtimeRate?: number;
}

// Tipe untuk produk yang ditampilkan
interface DisplayProduct {
  id: string;
  name: string;
  image?: string;
  price: number;
  capacity: string;
  type: string;
  fuelConsumption: string;
  availability: boolean;
  numCapacity: number;
  overtimeRate?: number;
}



export default function CatalogPage() {
  const [products, setProducts] = useState<DisplayProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [capacityFilter, setCapacityFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("default");

  useEffect(() => {
    async function loadProducts() {
      try {
        setLoading(true);

        // Minimum loading time untuk memastikan skeleton terlihat
        const [data] = await Promise.all([
          fetchProducts(),
          new Promise(resolve => setTimeout(resolve, 500)) // Minimum 500ms loading
        ]);

        // Periksa apakah data dan data.items ada
        if (!data || !data.items) {
          setProducts([]);
          console.error('Invalid data structure received from API');
          return;
        }

        // Transform data to display format
        const displayProducts = data.items.map((product: Product) => {
          const numCapacity = typeof product.capacity === 'number'
            ? product.capacity
            : parseInt(String(product.capacity).replace(/[^\d]/g, '')) || 0;

          return {
            id: product.id,
            name: product.name,
            price: product.price,
            numCapacity,
            capacity: extractCapacity(product.capacity),
            type: getGensetType(product.category),
            fuelConsumption: estimateFuelConsumption(product.capacity),
            availability: product.status === 'AVAILABLE',
            image: product.imageUrl || product.image || '',
            overtimeRate: product.overtimeRate || (product.price * 0.1), // Default to 10% of daily price if not available
          };
        });

        setProducts(displayProducts);
      } catch (error) {
        console.error('Error loading products:', error);
        setProducts([]);
      } finally {
        setLoading(false);
      }
    }

    loadProducts();
  }, []);

  // Filter function for products
  const filteredProducts = products.filter(product => {
    // Apply search filter
    if (searchQuery && searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      const matchesName = product.name.toLowerCase().includes(query);
      const matchesType = product.type.toLowerCase().includes(query);
      if (!matchesName && !matchesType) return false;
    }

    // Apply capacity filter
    if (capacityFilter && capacityFilter !== 'all') {
      if (capacityFilter === 'small' && product.numCapacity >= 50) return false;
      if (capacityFilter === 'medium' && (product.numCapacity < 50 || product.numCapacity > 150)) return false;
      if (capacityFilter === 'large' && product.numCapacity <= 150) return false;
    }

    // Apply type filter
    if (typeFilter && typeFilter !== 'all') {
      if (typeFilter.toLowerCase() !== product.type.toLowerCase()) return false;
    }

    return true;
  });

  // Sort function for products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-asc':
        return a.price - b.price;
      case 'price-desc':
        return b.price - a.price;
      case 'capacity-asc':
        return a.numCapacity - b.numCapacity;
      case 'capacity-desc':
        return b.numCapacity - a.numCapacity;
      case 'name-asc':
        return a.name.localeCompare(b.name);
      default:
        return 0; // Keep original order
    }
  });

  if (loading) {
    return <CatalogSkeletonComplete />;
  }

  return (
    <>
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat" style={{ backgroundImage: "url('/images/generator.svg')" }}></div>
        <div className="relative">
          <h1 className="text-3xl font-bold tracking-tight text-white">Katalog Genset</h1>
          <p className="text-white max-w-xl mt-2">Pilih genset yang sesuai kebutuhan Anda. Kami menyediakan berbagai ukuran dan tipe genset untuk kebutuhan industri.</p>
        </div>
      </div>

      <div className="mb-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div className="p-5 border-b border-gray-100 dark:border-gray-700 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-300">Filter Produk</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">Gunakan filter untuk menemukan genset yang tepat</p>
          </div>
          <Button variant="ghost" size="icon">
            <LuFilter className="h-4 w-4 text-violet-600 dark:text-violet-400" />
          </Button>
        </div>
        <div className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
            <div className="space-y-2">
              <div className="relative">
                <Input
                  id="search"
                  placeholder="Cari nama genset..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 border-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-white focus:ring-violet-500 focus:border-violet-500"
                />
                <LuSearch className="absolute left-2.5 top-2.5 h-4 w-4 text-blue-500 dark:text-blue-400 pointer-events-none" />
              </div>
            </div>

            <div className="space-y-2">
              <Select value={capacityFilter} onValueChange={setCapacityFilter}>
                <SelectTrigger className="border-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-white focus:ring-violet-500 focus:border-violet-500">
                  <SelectValue placeholder="Pilih kapasitas" />
                </SelectTrigger>
                <SelectContent className="z-[99999]">
                  <SelectItem value="all">Semua Kapasitas</SelectItem>
                  <SelectItem value="small">Kecil (&lt;50 kVA)</SelectItem>
                  <SelectItem value="medium">Menengah (50-150 kVA)</SelectItem>
                  <SelectItem value="large">Besar (&gt;150 kVA)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="border-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-white focus:ring-violet-500 focus:border-violet-500">
                  <SelectValue placeholder="Pilih tipe genset" />
                </SelectTrigger>
                <SelectContent className="z-[99999]">
                  <SelectItem value="all">Semua Tipe</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="silent">Silent</SelectItem>
                  <SelectItem value="portable">Portable</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2 flex items-end">
              <SoundButton
                variant="gradient"
                size="default"
                className="w-full h-11"
                soundType="click"
                onClick={() => {
                  // Filter sudah otomatis diterapkan melalui state changes
                  // Button ini bisa digunakan untuk reset atau aksi lain
                }}
              >
                <LuFilter className="mr-2 h-4 w-4" />
                Terapkan Filter
              </SoundButton>
            </div>
          </div>

          {/* Quick filter chips */}
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-400 mr-2">Filter Cepat:</span>
            <Badge
              className="cursor-pointer bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 dark:bg-yellow-900/30 dark:hover:bg-yellow-900/50 dark:text-yellow-400 dark:border-yellow-800"
              onClick={() => setCapacityFilter("small")}
            >
              Kapasitas Kecil
            </Badge>
            <Badge
              className="cursor-pointer bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:hover:bg-blue-900/50 dark:text-blue-400 dark:border-blue-800"
              onClick={() => setTypeFilter("silent")}
            >
              Silent Genset
            </Badge>
            <Badge
              className="cursor-pointer bg-green-50 hover:bg-green-100 text-green-700 border-green-200 dark:bg-green-900/30 dark:hover:bg-green-900/50 dark:text-green-400 dark:border-green-800"
              onClick={() => setSortBy("price-asc")}
            >
              Harga Terendah
            </Badge>
            <Badge
              className="cursor-pointer bg-red-50 hover:bg-red-100 text-red-700 border-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 dark:text-red-400 dark:border-red-800"
              onClick={() => setSortBy("capacity-desc")}
            >
              Paling Populer
            </Badge>
          </div>
        </div>
      </div>

      {sortedProducts.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 text-center">
          <div className="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
            <LuPackage className="h-10 w-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 dark:text-gray-300 mb-2">Tidak ada produk yang tersedia</h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mb-6">Silakan coba dengan filter yang berbeda atau hubungi admin untuk informasi ketersediaan genset.</p>
          <SoundButton onClick={() => {
            setCapacityFilter("all");
            setTypeFilter("all");
            setSearchQuery("");
            setSortBy("default");
          }} className="bg-violet-600 hover:bg-violet-700 text-white dark:text-gray-100" soundType="click">
            Hapus Semua Filter
          </SoundButton>
        </div>
      ) : (
        <div>
          <div className="flex justify-between items-center mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Menampilkan <span className="font-medium text-gray-900 dark:text-gray-300">{sortedProducts.length}</span> produk
            </p>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48 border-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-white">
                <SelectValue placeholder="Urutkan berdasarkan" />
              </SelectTrigger>
              <SelectContent className="z-[99999]">
                <SelectItem value="default">Urutan Default</SelectItem>
                <SelectItem value="price-asc">Harga: Terendah</SelectItem>
                <SelectItem value="price-desc">Harga: Tertinggi</SelectItem>
                <SelectItem value="capacity-asc">Kapasitas: Terendah</SelectItem>
                <SelectItem value="capacity-desc">Kapasitas: Tertinggi</SelectItem>
                <SelectItem value="name-asc">Nama: A-Z</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedProducts.map((product: DisplayProduct) => (
              <div key={product.id} className="group bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-200 flex flex-col">
                <div className="aspect-video w-full overflow-hidden bg-gray-50 dark:bg-gray-900 relative">
                  {product.image ? (
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      className="object-cover transition-all duration-300 group-hover:scale-105"
                      onError={(e) => {
                        // Jika gambar tidak bisa dimuat, ganti dengan placeholder
                        (e.target as HTMLImageElement).src = "https://via.placeholder.com/300x200?text=Genset";
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800">
                      <LuPackage className="h-12 w-12 text-gray-400 dark:text-gray-600" />
                    </div>
                  )}
                  <div className="absolute top-3 right-3">
                    <Badge className={`${product.availability ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}`}>
                      {product.availability ? "Tersedia" : "Tidak Tersedia"}
                    </Badge>
                  </div>
                </div>

                <div className="p-5">
                  <h3 className="font-bold text-gray-900 dark:text-white mb-1 truncate">{product.name}</h3>
                  <p className="text-green-600 dark:text-green-400 font-semibold text-lg mb-4">
                    {new Intl.NumberFormat('id-ID', {
                      style: 'currency',
                      currency: 'IDR',
                      maximumFractionDigits: 0
                    }).format(product.price)}
                    <span className="text-xs font-normal text-gray-500 dark:text-gray-400"> / 8 jam</span>
                  </p>

                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <div className="flex flex-col items-center p-2 bg-green-50 dark:bg-green-900/30 rounded-lg">
                      <LuPower className="h-5 w-5 text-green-500 dark:text-green-400 mb-1" />
                      <span className="text-xs text-green-700 dark:text-green-400 font-medium">{product.capacity}</span>
                    </div>

                    <div className="flex flex-col items-center p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                      <LuInfo className="h-5 w-5 text-blue-500 dark:text-blue-400 mb-1" />
                      <span className="text-xs text-blue-700 dark:text-blue-400 font-medium">{product.type}</span>
                    </div>

                    <div className="flex flex-col items-center p-2 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg">
                      <LuZap className="h-5 w-5 text-yellow-500 dark:text-yellow-400 mb-1" />
                      <span className="text-xs text-yellow-700 dark:text-yellow-400 font-medium">{product.fuelConsumption}</span>
                    </div>

                    <div className="flex flex-col items-center p-2 bg-red-50 dark:bg-red-900/30 rounded-lg">
                      <LuClock className="h-5 w-5 text-red-500 dark:text-red-400 mb-1" />
                      <span className="text-xs text-red-700 dark:text-red-400 font-medium">
                        {product.overtimeRate ? new Intl.NumberFormat('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          maximumFractionDigits: 0
                        }).format(product.overtimeRate) + '/jam' : '-'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-auto p-4 pt-0 flex gap-2">
                  <Link href={`/user/catalog/${product.id}`} className="flex-1">
                    <SoundButton variant="outline" size="default" className="w-full h-11" soundType="click">
                      Detail
                    </SoundButton>
                  </Link>
                  <Link href={`/user/catalog/${product.id}/rent`} className="flex-1">
                    <SoundButton
                      variant="gradient"
                      size="default"
                      className="w-full h-11"
                      disabled={!product.availability}
                      soundType={product.availability ? "click" : "none"}
                    >
                      Sewa
                    </SoundButton>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-10 pb-10 flex justify-center">
            <SoundButton variant="outline" size="default" className="flex items-center gap-2 h-11" soundType="click">
              Lihat Lebih Banyak <LuArrowRight className="h-4 w-4 ml-1" />
            </SoundButton>
          </div>
        </div>
      )}
    </>
  );
}

async function fetchProducts() {
  try {
    const response = await fetch(`/api/products`, {
      cache: 'no-store',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 60 } // Revalidate setiap 60 detik
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API responded with status ${response.status}: ${errorText}`);
      throw new Error(`Failed to fetch products: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error loading products:', error);
    // Jangan throw error, kembalikan array kosong
    return { items: [] };
  }
}

// Komponen Skeleton Loading Lengkap untuk Catalog
function CatalogSkeletonComplete() {
  return (
    <>
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat"></div>
        <div className="relative">
          <Skeleton className="h-8 w-48 mb-2 animate-pulse" />
          <Skeleton className="h-4 w-96 animate-pulse" />
        </div>
      </div>

      {/* Filter Section Skeleton */}
      <div className="mb-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div className="p-5 border-b border-gray-100 dark:border-gray-700 flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-32 mb-1 animate-pulse" />
            <Skeleton className="h-4 w-64 animate-pulse" />
          </div>
          <Skeleton className="h-8 w-8 animate-pulse" />
        </div>
        <div className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
            {/* Search Input Skeleton */}
            <div className="space-y-2">
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Capacity Filter Skeleton */}
            <div className="space-y-2">
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Type Filter Skeleton */}
            <div className="space-y-2">
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Apply Button Skeleton */}
            <div className="space-y-2 flex items-end">
              <Skeleton className="h-11 w-full animate-pulse" />
            </div>
          </div>

          {/* Quick Filter Chips Skeleton */}
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
            <Skeleton className="h-4 w-24 mr-2 animate-pulse" />
            <Skeleton className="h-6 w-20 rounded-full animate-pulse" />
            <Skeleton className="h-6 w-24 rounded-full animate-pulse" />
            <Skeleton className="h-6 w-28 rounded-full animate-pulse" />
            <Skeleton className="h-6 w-26 rounded-full animate-pulse" />
          </div>
        </div>
      </div>

      {/* Results Header Skeleton */}
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="h-4 w-40 animate-pulse" />
        <Skeleton className="h-10 w-48 animate-pulse" />
      </div>

      {/* Product Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="group bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden flex flex-col">
            {/* Product Image Skeleton */}
            <div className="aspect-video w-full overflow-hidden bg-gray-50 dark:bg-gray-900 relative">
              <Skeleton className="w-full h-full animate-pulse" />
              <div className="absolute top-3 right-3">
                <Skeleton className="h-6 w-20 rounded-full animate-pulse" />
              </div>
            </div>

            {/* Product Content Skeleton */}
            <div className="p-5">
              <Skeleton className="h-6 w-3/4 mb-1 animate-pulse" />
              <Skeleton className="h-7 w-32 mb-4 animate-pulse" />

              {/* Product Features Grid Skeleton */}
              <div className="grid grid-cols-2 gap-3 mb-4">
                {Array.from({ length: 4 }).map((_, j) => (
                  <div key={j} className="flex flex-col items-center p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Skeleton className="h-5 w-5 mb-1 animate-pulse" />
                    <Skeleton className="h-3 w-12 animate-pulse" />
                  </div>
                ))}
              </div>
            </div>

            {/* Product Actions Skeleton */}
            <div className="mt-auto p-4 pt-0 flex gap-2">
              <Skeleton className="h-11 flex-1 animate-pulse" />
              <Skeleton className="h-11 flex-1 animate-pulse" />
            </div>
          </div>
        ))}
      </div>

      {/* Load More Button Skeleton */}
      <div className="mt-10 pb-10 flex justify-center">
        <Skeleton className="h-11 w-40 animate-pulse" />
      </div>
    </>
  );
}
